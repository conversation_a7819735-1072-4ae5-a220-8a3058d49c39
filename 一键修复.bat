@echo off
chcp 65001 >nul
title 虚拟机启动问题修复工具

echo ========================================
echo           虚拟机启动问题修复工具
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ 已获得管理员权限
) else (
    echo ✗ 需要管理员权限，正在重新启动...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

echo.
echo 正在修复虚拟化问题...
echo.

echo [1/4] 禁用虚拟化安全性...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\DeviceGuard" /v EnableVirtualizationBasedSecurity /t REG_DWORD /d 0 /f >nul 2>&1
if %errorlevel% == 0 (
    echo ✓ 虚拟化安全性已禁用
) else (
    echo ✗ 禁用虚拟化安全性失败
)

echo.
echo [2/4] 禁用内存完整性...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\DeviceGuard\Scenarios\HypervisorEnforcedCodeIntegrity" /v Enabled /t REG_DWORD /d 0 /f >nul 2>&1
if %errorlevel% == 0 (
    echo ✓ 内存完整性已禁用
) else (
    echo ✗ 禁用内存完整性失败
)

echo.
echo [3/4] 设置hypervisor启动类型...
bcdedit /set hypervisorlaunchtype off >nul 2>&1
if %errorlevel% == 0 (
    echo ✓ Hypervisor启动类型已设置为off
) else (
    echo ✗ 设置hypervisor失败（可能需要重启后再试）
)

echo.
echo [4/4] 检查Hyper-V功能...
dism /online /get-featureinfo /featurename:Microsoft-Hyper-V-All >nul 2>&1
if %errorlevel% == 0 (
    echo ! 检测到Hyper-V功能，建议手动禁用
    echo   请打开"启用或关闭Windows功能"，取消勾选Hyper-V
) else (
    echo ✓ Hyper-V功能未启用
)

echo.
echo ========================================
echo                修复完成！
echo ========================================
echo.
echo 重要提示：
echo 1. 请立即重启计算机以使更改生效
echo 2. 重启后尝试启动您的虚拟机
echo 3. 如果问题仍然存在，请查看manual_fix_guide.txt
echo.
echo 按任意键退出...
pause >nul
