# Simple virtualization fix script
Write-Host "Fixing virtualization issues..." -ForegroundColor Green

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "This script requires administrator privileges. Restarting..." -ForegroundColor Red
    Start-Process PowerShell -Verb RunAs "-File `"$PSCommandPath`""
    exit
}

Write-Host "Running with administrator privileges" -ForegroundColor Green

# Disable VBS
Write-Host "Disabling Virtualization Based Security..." -ForegroundColor Yellow
try {
    Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\DeviceGuard" -Name "EnableVirtualizationBasedSecurity" -Value 0 -Type DWord -Force
    Write-Host "VBS disabled successfully" -ForegroundColor Green
} catch {
    Write-Host "Failed to disable VBS: $($_.Exception.Message)" -ForegroundColor Red
}

# Disable Memory Integrity
Write-Host "Disabling Memory Integrity..." -ForegroundColor Yellow
try {
    $path = "HKLM:\SYSTEM\CurrentControlSet\Control\DeviceGuard\Scenarios\HypervisorEnforcedCodeIntegrity"
    if (!(Test-Path $path)) {
        New-Item -Path $path -Force | Out-Null
    }
    Set-ItemProperty -Path $path -Name "Enabled" -Value 0 -Type DWord -Force
    Write-Host "Memory Integrity disabled successfully" -ForegroundColor Green
} catch {
    Write-Host "Failed to disable Memory Integrity: $($_.Exception.Message)" -ForegroundColor Red
}

# Set hypervisor launch type to off
Write-Host "Setting hypervisor launch type to off..." -ForegroundColor Yellow
try {
    $result = cmd /c "bcdedit /set hypervisorlaunchtype off" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Hypervisor launch type set to off successfully" -ForegroundColor Green
    } else {
        Write-Host "Failed to set hypervisor: $result" -ForegroundColor Red
    }
} catch {
    Write-Host "Failed to set hypervisor: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nFix completed!" -ForegroundColor Green
Write-Host "Please restart your computer for changes to take effect." -ForegroundColor Yellow
Write-Host "After restart, your virtual machine should start normally." -ForegroundColor Yellow

Read-Host "`nPress Enter to exit"
