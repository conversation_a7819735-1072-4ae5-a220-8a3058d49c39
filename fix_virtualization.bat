@echo off
echo 正在修复虚拟化问题...
echo 请以管理员身份运行此脚本

echo.
echo 步骤1: 禁用虚拟化安全性
reg add "HKLM\SYSTEM\CurrentControlSet\Control\DeviceGuard" /v EnableVirtualizationBasedSecurity /t REG_DWORD /d 0 /f
if %errorlevel% neq 0 (
    echo 错误: 需要管理员权限来修改注册表
    pause
    exit /b 1
)

echo.
echo 步骤2: 禁用内存完整性
reg add "HKLM\SYSTEM\CurrentControlSet\Control\DeviceGuard\Scenarios\HypervisorEnforcedCodeIntegrity" /v Enabled /t REG_DWORD /d 0 /f

echo.
echo 步骤3: 设置hypervisor启动类型为off
bcdedit /set hypervisorlaunchtype off
if %errorlevel% neq 0 (
    echo 警告: 无法修改启动配置，可能需要重新启动后再试
)

echo.
echo 步骤4: 禁用Hyper-V功能
dism /online /disable-feature /featurename:Microsoft-Hyper-V-All /norestart

echo.
echo 修复完成！请重新启动计算机以使更改生效。
echo 重启后，您的虚拟机应该能够正常启动。
echo.
pause
