# 检查是否以管理员身份运行
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator"))
{
    Write-Host "此脚本需要管理员权限。正在重新启动..." -ForegroundColor Red
    Start-Process PowerShell -Verb RunAs "-File `"$PSCommandPath`""
    exit
}

Write-Host "正在修复虚拟化问题..." -ForegroundColor Green

# 禁用虚拟化安全性
Write-Host "步骤1: 禁用虚拟化安全性" -ForegroundColor Yellow
try {
    Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\DeviceGuard" -Name "EnableVirtualizationBasedSecurity" -Value 0 -Type DWord
    Write-Host "✓ 虚拟化安全性已禁用" -ForegroundColor Green
} catch {
    Write-Host "✗ 禁用虚拟化安全性失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 禁用内存完整性
Write-Host "步骤2: 禁用内存完整性" -ForegroundColor Yellow
try {
    $path = "HKLM:\SYSTEM\CurrentControlSet\Control\DeviceGuard\Scenarios\HypervisorEnforcedCodeIntegrity"
    if (!(Test-Path $path)) {
        New-Item -Path $path -Force | Out-Null
    }
    Set-ItemProperty -Path $path -Name "Enabled" -Value 0 -Type DWord
    Write-Host "✓ 内存完整性已禁用" -ForegroundColor Green
} catch {
    Write-Host "✗ 禁用内存完整性失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 设置hypervisor启动类型
Write-Host "步骤3: 设置hypervisor启动类型" -ForegroundColor Yellow
try {
    $result = & bcdedit /set hypervisorlaunchtype off 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Hypervisor启动类型已设置为off" -ForegroundColor Green
    } else {
        Write-Host "✗ 设置hypervisor失败: $result" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ 设置hypervisor失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 禁用Hyper-V功能
Write-Host "步骤4: 禁用Hyper-V功能" -ForegroundColor Yellow
try {
    $features = Get-WindowsOptionalFeature -Online | Where-Object {$_.FeatureName -like "*Hyper-V*" -and $_.State -eq "Enabled"}
    if ($features) {
        foreach ($feature in $features) {
            Disable-WindowsOptionalFeature -Online -FeatureName $feature.FeatureName -NoRestart
            Write-Host "✓ 已禁用 $($feature.FeatureName)" -ForegroundColor Green
        }
    } else {
        Write-Host "✓ Hyper-V功能已经被禁用" -ForegroundColor Green
    }
} catch {
    Write-Host "✗ 禁用Hyper-V功能失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n修复完成！" -ForegroundColor Green
Write-Host "Please restart your computer for changes to take effect." -ForegroundColor Yellow
Write-Host "After restart, your virtual machine should start normally." -ForegroundColor Yellow

Read-Host "Press any key to exit"
